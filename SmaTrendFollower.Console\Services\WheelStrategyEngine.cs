using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Production-ready wheel options strategy engine
/// Systematically sells cash-secured puts and covered calls to generate income
/// </summary>
public sealed class WheelStrategyEngine : BackgroundService, IWheelStrategyEngine, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IMarketSessionGuard _sessionGuard;
    private readonly ITradeExecutor _tradeExecutor;
    private readonly IVIXResolverService _vixResolver;
    private readonly IDiscordNotificationService _discordService;
    private readonly ILogger<WheelStrategyEngine> _logger;
    private readonly IOptionsMonitor<WheelStrategyConfig> _configMonitor;

    private readonly ConcurrentDictionary<string, WheelPosition> _positions = new();
    private readonly SemaphoreSlim _executionLock = new(1, 1);
    private WheelStrategyConfig _config;
    private bool _disposed;

    public WheelStrategyEngine(
        IMarketDataService marketDataService,
        IMarketSessionGuard sessionGuard,
        ITradeExecutor tradeExecutor,
        IVIXResolverService vixResolver,
        IDiscordNotificationService discordService,
        ILogger<WheelStrategyEngine> logger,
        IOptionsMonitor<WheelStrategyConfig> configMonitor)
    {
        _marketDataService = marketDataService;
        _sessionGuard = sessionGuard;
        _tradeExecutor = tradeExecutor;
        _vixResolver = vixResolver;
        _discordService = discordService;
        _logger = logger;
        _configMonitor = configMonitor;
        _config = configMonitor.CurrentValue;

        // Monitor configuration changes
        configMonitor.OnChange(config => _config = config);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("🎡 Wheel Strategy Engine starting...");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                if (!_config.Enabled)
                {
                    await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
                    continue;
                }

                var canTrade = await _sessionGuard.CanTradeNowAsync();
                if (!canTrade)
                {
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                    continue;
                }

                await ManageExistingPositionsAsync(stoppingToken);
                await EvaluateNewOpportunitiesAsync(stoppingToken);

                // Run every 15 minutes during market hours
                await Task.Delay(TimeSpan.FromMinutes(15), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in wheel strategy execution cycle");
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
        }

        _logger.LogInformation("🎡 Wheel Strategy Engine stopped");
    }

    public async Task<WheelCycleResult> RunWheelCycleAsync(string symbol, CancellationToken cancellationToken = default)
    {
        if (!await IsSymbolSuitableForWheelAsync(symbol))
        {
            return new WheelCycleResult(symbol, WheelAction.NoAction, false, "Symbol not suitable for wheel strategy");
        }

        await _executionLock.WaitAsync(cancellationToken);
        try
        {
            var existingPosition = _positions.GetValueOrDefault(symbol);
            
            if (existingPosition != null)
            {
                return await ManageExistingPosition(existingPosition, cancellationToken);
            }

            return await InitiateNewWheelCycle(symbol, cancellationToken);
        }
        finally
        {
            _executionLock.Release();
        }
    }

    public Task<IEnumerable<WheelPosition>> GetCurrentPositionsAsync()
    {
        return Task.FromResult(_positions.Values.AsEnumerable());
    }

    public Task<WheelPosition?> GetPositionAsync(string symbol)
    {
        _positions.TryGetValue(symbol, out var position);
        return Task.FromResult(position);
    }

    public async Task ManageExistingPositionsAsync(CancellationToken cancellationToken = default)
    {
        var positions = _positions.Values.ToList();
        
        foreach (var position in positions)
        {
            try
            {
                await ManageExistingPosition(position, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error managing wheel position for {Symbol}", position.Symbol);
            }
        }
    }

    public async Task<bool> IsSymbolSuitableForWheelAsync(string symbol)
    {
        try
        {
            // Check if symbol is in allowed list
            if (_config.AllowedSymbols?.Any() == true && !_config.AllowedSymbols.Contains(symbol))
                return false;

            // Check if symbol is excluded
            if (_config.ExcludedSymbols?.Contains(symbol) == true)
                return false;

            // Get current price from recent bars (since there's no GetQuoteAsync)
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-1);
            var bars = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);

            if (bars?.Items?.Any() != true)
                return false;

            var latestBar = bars.Items.Last();
            var currentPrice = latestBar.Close;
            if (currentPrice < 10m)
                return false;

            // Check if options are available
            var optionsData = await GetOptionsDataAsync(symbol);
            if (!optionsData.Any())
                return false;

            // Check implied volatility if required
            if (_config.RequireHighIV)
            {
                var avgIV = optionsData.Average(o => o.ImpliedVolatility ?? 0);
                if (avgIV < _config.MinIVPercentile / 100m)
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking symbol suitability for {Symbol}", symbol);
            return false;
        }
    }

    public Task<WheelPerformanceMetrics> GetPerformanceMetricsAsync()
    {
        var positions = _positions.Values.ToList();
        
        var totalPremium = positions.Sum(p => p.Premium);
        var unrealizedPnL = positions.Where(p => p.Status == WheelPositionStatus.Active).Sum(p => p.UnrealizedPnL);
        var realizedPnL = positions.Where(p => p.Status != WheelPositionStatus.Active).Sum(p => p.UnrealizedPnL);
        var activePositions = positions.Count(p => p.Status == WheelPositionStatus.Active);
        var completedCycles = positions.Count(p => p.Status == WheelPositionStatus.Closed);

        // Calculate annualized return (simplified)
        var totalDays = positions.Any() ? (DateTime.UtcNow - positions.Min(p => p.CreatedAt)).Days : 1;
        var annualizedReturn = totalDays > 0 ? (totalPremium + realizedPnL) * 365m / totalDays / 100000m : 0m;

        var winRate = completedCycles > 0 ? positions.Count(p => p.Status == WheelPositionStatus.Closed && p.UnrealizedPnL > 0) / (decimal)completedCycles : 0m;
        var avgPremium = completedCycles > 0 ? totalPremium / completedCycles : 0m;

        return Task.FromResult(new WheelPerformanceMetrics(
            totalPremium,
            unrealizedPnL,
            realizedPnL,
            activePositions,
            completedCycles,
            annualizedReturn,
            winRate,
            avgPremium,
            DateTime.UtcNow
        ));
    }

    public Task UpdateConfigurationAsync(WheelStrategyConfig config)
    {
        _config = config;
        _logger.LogInformation("Wheel strategy configuration updated");
        return Task.CompletedTask;
    }

    private async Task<WheelCycleResult> InitiateNewWheelCycle(string symbol, CancellationToken cancellationToken)
    {
        // Check if we have stock position first
        var positions = await _marketDataService.GetPositionsAsync();
        var stockPosition = positions.FirstOrDefault(p => p.Symbol == symbol && !p.Symbol.Contains("C") && !p.Symbol.Contains("P"));

        if (stockPosition?.Quantity > 0)
        {
            // We own stock, sell covered call
            return await SellCoveredCall(symbol, stockPosition.Quantity, cancellationToken);
        }
        else
        {
            // No stock position, sell cash-secured put
            return await SellCashSecuredPut(symbol, cancellationToken);
        }
    }

    private async Task<WheelCycleResult> ManageExistingPosition(WheelPosition position, CancellationToken cancellationToken)
    {
        // Check if position needs to be rolled or closed
        var daysToExpiration = (position.Expiration - DateTime.UtcNow).Days;
        
        if (daysToExpiration <= 7 && _config.EnableRolling)
        {
            return await ConsiderRollingPosition(position, cancellationToken);
        }

        // Check for early profit taking
        if (position.UnrealizedPnL > position.Premium * _config.RollThreshold)
        {
            return await ClosePosition(position, cancellationToken);
        }

        return new WheelCycleResult(position.Symbol, WheelAction.NoAction, true, "Position being monitored");
    }

    private async Task EvaluateNewOpportunitiesAsync(CancellationToken cancellationToken)
    {
        // Get suitable symbols for wheel strategy
        var universe = await GetWheelUniverseAsync();
        
        foreach (var symbol in universe.Take(5)) // Limit to 5 evaluations per cycle
        {
            if (_positions.ContainsKey(symbol))
                continue;

            try
            {
                await RunWheelCycleAsync(symbol, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error evaluating wheel opportunity for {Symbol}", symbol);
            }
        }
    }

    private async Task<WheelCycleResult> SellCashSecuredPut(string symbol, CancellationToken cancellationToken)
    {
        try
        {
            // Get current price from recent bars
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-1);
            var bars = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);

            if (bars?.Items?.Any() != true)
                return new WheelCycleResult(symbol, WheelAction.NoAction, false, "No recent price data available");

            var currentPrice = bars.Items.Last().Close;
            var optionsData = await GetOptionsDataAsync(symbol);

            // Find suitable put to sell
            var targetStrike = currentPrice * 0.95m; // 5% OTM
            var suitablePut = optionsData
                .Where(o => o.Symbol.Contains("P"))
                .Where(o => o.Strike <= targetStrike)
                .Where(o => o.Delta <= _config.MaxDeltaForPuts)
                .Where(o => (o.ExpirationDate - DateTime.UtcNow).Days >= _config.MinDaysToExpiration)
                .Where(o => (o.ExpirationDate - DateTime.UtcNow).Days <= _config.MaxDaysToExpiration)
                .Where(o => o.Bid.HasValue && o.Bid > currentPrice * _config.MinPremiumPercent)
                .OrderByDescending(o => o.Bid)
                .FirstOrDefault();

            if (suitablePut.Equals(default(OptionData)) || string.IsNullOrEmpty(suitablePut.Symbol))
                return new WheelCycleResult(symbol, WheelAction.NoAction, false, "No suitable put found");

            // Calculate position size
            var account = await _marketDataService.GetAccountAsync();
            var maxAllocation = account.Equity * _config.MaxAllocationPercent;
            var contractsToSell = Math.Floor(maxAllocation / (suitablePut.Strike * 100m));

            if (contractsToSell < 1)
                return new WheelCycleResult(symbol, WheelAction.NoAction, false, "Insufficient capital");

            // Execute the trade
            var premium = suitablePut.Bid ?? 0;
            var totalPremium = contractsToSell * premium * 100;

            _logger.LogInformation("🎡 Selling {Contracts} cash-secured puts for {Symbol} at ${Strike} for ${Premium} premium",
                contractsToSell, symbol, suitablePut.Strike, totalPremium);

            // Create wheel position record
            var position = new WheelPosition(
                symbol,
                WheelPositionType.CashSecuredPut,
                contractsToSell,
                suitablePut.Strike,
                suitablePut.ExpirationDate,
                suitablePut.Symbol,
                totalPremium,
                0, // Initial unrealized P&L
                DateTime.UtcNow,
                WheelPositionStatus.Active
            );

            _positions[symbol] = position;

            await _discordService.SendMessageAsync($"🎡 Wheel: Sold {contractsToSell} CSP {symbol} ${suitablePut.Strike} for ${totalPremium:F0}");

            return new WheelCycleResult(symbol, WheelAction.SellCashSecuredPut, true, "Cash-secured put sold",
                totalPremium, suitablePut.Symbol, suitablePut.Strike, suitablePut.ExpirationDate, contractsToSell);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error selling cash-secured put for {Symbol}", symbol);
            return new WheelCycleResult(symbol, WheelAction.SellCashSecuredPut, false, $"Error: {ex.Message}");
        }
    }

    private async Task<WheelCycleResult> SellCoveredCall(string symbol, decimal sharesOwned, CancellationToken cancellationToken)
    {
        try
        {
            // Get current price from recent bars
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-1);
            var bars = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);

            if (bars?.Items?.Any() != true)
                return new WheelCycleResult(symbol, WheelAction.NoAction, false, "No recent price data available");

            var currentPrice = bars.Items.Last().Close;
            var optionsData = await GetOptionsDataAsync(symbol);

            // Find suitable call to sell
            var targetStrike = currentPrice * 1.05m; // 5% OTM
            var suitableCall = optionsData
                .Where(o => o.Symbol.Contains("C"))
                .Where(o => o.Strike >= targetStrike)
                .Where(o => o.Delta <= _config.MaxDeltaForCalls)
                .Where(o => (o.ExpirationDate - DateTime.UtcNow).Days >= _config.MinDaysToExpiration)
                .Where(o => (o.ExpirationDate - DateTime.UtcNow).Days <= _config.MaxDaysToExpiration)
                .Where(o => o.Bid.HasValue && o.Bid > currentPrice * _config.MinPremiumPercent)
                .OrderByDescending(o => o.Bid)
                .FirstOrDefault();

            if (suitableCall.Equals(default(OptionData)) || string.IsNullOrEmpty(suitableCall.Symbol))
                return new WheelCycleResult(symbol, WheelAction.NoAction, false, "No suitable call found");

            // Calculate contracts to sell (1 contract per 100 shares)
            var contractsToSell = Math.Floor(sharesOwned / 100);

            if (contractsToSell < 1)
                return new WheelCycleResult(symbol, WheelAction.NoAction, false, "Insufficient shares for covered call");

            var premium = suitableCall.Bid ?? 0;
            var totalPremium = contractsToSell * premium * 100;

            _logger.LogInformation("🎡 Selling {Contracts} covered calls for {Symbol} at ${Strike} for ${Premium} premium",
                contractsToSell, symbol, suitableCall.Strike, totalPremium);

            // Create wheel position record
            var position = new WheelPosition(
                symbol,
                WheelPositionType.CoveredCall,
                contractsToSell,
                suitableCall.Strike,
                suitableCall.ExpirationDate,
                suitableCall.Symbol,
                totalPremium,
                0, // Initial unrealized P&L
                DateTime.UtcNow,
                WheelPositionStatus.Active
            );

            _positions[symbol] = position;

            await _discordService.SendMessageAsync($"🎡 Wheel: Sold {contractsToSell} CC {symbol} ${suitableCall.Strike} for ${totalPremium:F0}");

            return new WheelCycleResult(symbol, WheelAction.SellCoveredCall, true, "Covered call sold",
                totalPremium, suitableCall.Symbol, suitableCall.Strike, suitableCall.ExpirationDate, contractsToSell);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error selling covered call for {Symbol}", symbol);
            return new WheelCycleResult(symbol, WheelAction.SellCoveredCall, false, $"Error: {ex.Message}");
        }
    }

    private async Task<WheelCycleResult> ConsiderRollingPosition(WheelPosition position, CancellationToken cancellationToken)
    {
        try
        {
            // Check if position is profitable enough to close instead of roll
            if (position.UnrealizedPnL > position.Premium * 0.75m)
            {
                return await ClosePosition(position, cancellationToken);
            }

            // Find next expiration cycle to roll to
            var optionsData = await GetOptionsDataAsync(position.Symbol);
            var nextExpiration = position.Expiration.AddDays(7); // Roll to next week

            var rollCandidate = optionsData
                .Where(o => position.Type == WheelPositionType.CashSecuredPut ? o.Symbol.Contains("P") : o.Symbol.Contains("C"))
                .Where(o => o.ExpirationDate >= nextExpiration)
                .Where(o => Math.Abs(o.Strike - position.Strike) < 0.01m) // Same strike
                .OrderBy(o => o.ExpirationDate)
                .FirstOrDefault();

            if (!rollCandidate.Equals(default(OptionData)) && rollCandidate.Bid.HasValue && rollCandidate.Bid > 0)
            {
                _logger.LogInformation("🎡 Rolling {Type} position for {Symbol} to {Expiration}",
                    position.Type, position.Symbol, rollCandidate.ExpirationDate);

                // Update position with new expiration
                var rolledPosition = position with
                {
                    Expiration = rollCandidate.ExpirationDate,
                    OptionSymbol = rollCandidate.Symbol,
                    Status = WheelPositionStatus.Rolled
                };

                _positions[position.Symbol] = rolledPosition;

                return new WheelCycleResult(position.Symbol, WheelAction.RollPosition, true, "Position rolled",
                    rollCandidate.Bid * position.Quantity * 100, rollCandidate.Symbol, rollCandidate.Strike, rollCandidate.ExpirationDate);
            }

            return new WheelCycleResult(position.Symbol, WheelAction.NoAction, false, "No suitable roll candidate found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rolling position for {Symbol}", position.Symbol);
            return new WheelCycleResult(position.Symbol, WheelAction.RollPosition, false, $"Error: {ex.Message}");
        }
    }

    private async Task<WheelCycleResult> ClosePosition(WheelPosition position, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("🎡 Closing {Type} position for {Symbol} with P&L: ${PnL}",
                position.Type, position.Symbol, position.UnrealizedPnL);

            // Mark position as closed
            var closedPosition = position with { Status = WheelPositionStatus.Closed };
            _positions[position.Symbol] = closedPosition;

            await _discordService.SendMessageAsync($"🎡 Wheel: Closed {position.Type} {position.Symbol} P&L: ${position.UnrealizedPnL:F0}");

            return new WheelCycleResult(position.Symbol, WheelAction.ClosePosition, true, "Position closed for profit");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error closing position for {Symbol}", position.Symbol);
            return new WheelCycleResult(position.Symbol, WheelAction.ClosePosition, false, $"Error: {ex.Message}");
        }
    }

    private Task<IEnumerable<string>> GetWheelUniverseAsync()
    {
        // Implementation for getting suitable symbols
        var defaultSymbols = new[] { "SPY", "QQQ", "AAPL", "MSFT", "TSLA", "NVDA", "AMD", "AMZN" };
        return Task.FromResult(defaultSymbols.AsEnumerable());
    }

    private async Task<IEnumerable<OptionData>> GetOptionsDataAsync(string symbol)
    {
        // Use the market data service to get options data
        return await _marketDataService.GetOptionsDataAsync(symbol);
    }

    public override void Dispose()
    {
        if (!_disposed)
        {
            _executionLock?.Dispose();
            _disposed = true;
        }
        base.Dispose();
    }
}
