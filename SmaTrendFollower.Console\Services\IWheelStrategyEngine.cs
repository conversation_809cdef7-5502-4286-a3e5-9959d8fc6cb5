using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for the wheel options strategy engine
/// Manages cash-secured puts and covered calls in a systematic cycle
/// </summary>
public interface IWheelStrategyEngine
{
    /// <summary>
    /// Evaluates and executes the next wheel cycle for a symbol
    /// </summary>
    Task<WheelCycleResult> RunWheelCycleAsync(string symbol, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets current wheel positions for all symbols
    /// </summary>
    Task<IEnumerable<WheelPosition>> GetCurrentPositionsAsync();

    /// <summary>
    /// Gets current wheel position for a specific symbol
    /// </summary>
    Task<WheelPosition?> GetPositionAsync(string symbol);

    /// <summary>
    /// Manages existing wheel positions (rolling, closing, etc.)
    /// </summary>
    Task ManageExistingPositionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Evaluates if a symbol is suitable for wheel strategy
    /// </summary>
    Task<bool> IsSymbolSuitableForWheelAsync(string symbol);

    /// <summary>
    /// Gets wheel strategy performance metrics
    /// </summary>
    Task<WheelPerformanceMetrics> GetPerformanceMetricsAsync();

    /// <summary>
    /// Updates wheel strategy configuration
    /// </summary>
    Task UpdateConfigurationAsync(WheelStrategyConfig config);
}

/// <summary>
/// Represents a wheel position for a symbol
/// </summary>
public record WheelPosition(
    string Symbol,
    WheelPositionType Type,
    decimal Quantity,
    decimal Strike,
    DateTime Expiration,
    string OptionSymbol,
    decimal Premium,
    decimal UnrealizedPnL,
    DateTime CreatedAt,
    WheelPositionStatus Status,
    decimal? AssignmentPrice = null,
    DateTime? AssignmentDate = null
);

/// <summary>
/// Result of a wheel cycle execution
/// </summary>
public record WheelCycleResult(
    string Symbol,
    WheelAction Action,
    bool Success,
    string Reason,
    decimal? Premium = null,
    string? OptionSymbol = null,
    decimal? Strike = null,
    DateTime? Expiration = null,
    decimal? Quantity = null
);

/// <summary>
/// Wheel strategy performance metrics
/// </summary>
public record WheelPerformanceMetrics(
    decimal TotalPremiumCollected,
    decimal UnrealizedPnL,
    decimal RealizedPnL,
    int ActivePositions,
    int CompletedCycles,
    decimal AnnualizedReturn,
    decimal WinRate,
    decimal AveragePremiumPerCycle,
    DateTime LastUpdated
);

/// <summary>
/// Configuration for wheel strategy
/// </summary>
public record WheelStrategyConfig(
    bool Enabled = true,
    decimal MaxAllocationPercent = 0.20m,
    decimal MinPremiumPercent = 0.01m,
    int MinDaysToExpiration = 7,
    int MaxDaysToExpiration = 45,
    decimal MaxDeltaForPuts = 0.30m,
    decimal MaxDeltaForCalls = 0.30m,
    decimal MinLiquidity = 100m,
    decimal MaxBidAskSpreadPercent = 0.05m,
    bool EnableRolling = true,
    decimal RollThreshold = 0.50m,
    int MaxRollAttempts = 2,
    bool RequireHighIV = true,
    decimal MinIVPercentile = 30m,
    IReadOnlyList<string>? AllowedSymbols = null,
    IReadOnlyList<string>? ExcludedSymbols = null
);

/// <summary>
/// Type of wheel position
/// </summary>
public enum WheelPositionType
{
    CashSecuredPut,
    CoveredCall
}

/// <summary>
/// Status of wheel position
/// </summary>
public enum WheelPositionStatus
{
    Active,
    Assigned,
    Expired,
    Closed,
    Rolled
}

/// <summary>
/// Action taken in wheel cycle
/// </summary>
public enum WheelAction
{
    SellCashSecuredPut,
    SellCoveredCall,
    RollPosition,
    ClosePosition,
    NoAction,
    WaitForAssignment,
    WaitForExpiration
}
